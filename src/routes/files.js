import { Router } from 'express';
import <PERSON><PERSON> from 'joi';
import { FileController } from '../controllers/FileController.js';
import { validateParams, validateQuery } from '../middleware/validation.js';
import { rateLimitPerUser, extractClientIP } from '../middleware/auth.js';

const router = Router();

// Validation schemas
const fileIdSchema = Joi.object({
  fileId: Joi.string().required().min(1).max(255)
});

const downloadQuerySchema = Joi.object({
  download: Joi.string().valid('true', 'false').optional()
});

/**
 * Handle CORS preflight requests for file access
 * OPTIONS /api/files/:fileId
 */
router.options('/:fileId', (req, res) => {
  const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'];
  const origin = req.headers.origin;

  if (allowedOrigins.includes(origin)) {
    res.setHeader('Access-Control-Allow-Origin', origin);
  }
  res.setHeader('Access-Control-Allow-Credentials', 'true');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Session-ID, X-CSRF-Token');
  res.setHeader('Access-Control-Max-Age', '86400'); // 24 hours
  res.status(200).end();
});

/**
 * Serve file directly through our secure API
 * GET /api/files/:fileId
 */
router.get('/:fileId',
  extractClientIP, // Extract client IP for rate limiting
  validateParams(fileIdSchema),
  rateLimitPerUser(100, 60 * 1000), // 100 file access requests per minute
  FileController.getFileAccess
);

export default router;
