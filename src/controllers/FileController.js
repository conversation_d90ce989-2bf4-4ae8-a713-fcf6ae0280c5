import { asyncHand<PERSON> } from '../middleware/errorHandler.js';
import { ResponseUtil } from '../utils/response.js';
import { S3Service } from '../services/S3Service.js';
import { ChatMessage } from '../models/chat/ChatMessage.js';
import logger from '../config/logger.js';
import path from 'path';
import fs from 'fs/promises';

/**
 * File Controller
 * Handles secure file access and download operations
 */
class FileController {
  /**
   * Serve file directly through our API (secure proxy)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static getFileAccess = asyncHandler(async (req, res) => {
    const { fileId } = req.params;

    if (!fileId) {
      ResponseUtil.badRequest(res, 'File ID is required');
      return;
    }

    try {
      // First, try to get file from secure mapping
      let fileMapping = null;
      let chatMessage = null;

      if (S3Service.isAvailable()) {
        fileMapping = S3Service.getSecureFileMapping(fileId);
      }

      // If not in memory mapping, look up in database
      if (!fileMapping) {
        chatMessage = await ChatMessage.findOne({
          where: { attachmentSecureId: fileId }
        });

        if (!chatMessage) {
          ResponseUtil.notFound(res, 'File not found or access denied');
          return;
        }
      }

      // Serve S3 file by proxying through our API
      if (fileMapping || (chatMessage && chatMessage.attachmentStorageType === 's3')) {
        if (!S3Service.isAvailable()) {
          ResponseUtil.serverError(res, 'File storage service is not available');
          return;
        }

        const s3Key = fileMapping ? fileMapping.s3Key : chatMessage.attachmentS3Key;
        const originalName = fileMapping ? fileMapping.originalName : chatMessage.attachmentName;
        const mimeType = fileMapping ? fileMapping.mimeType : chatMessage.attachmentType;

        try {
          // Download file from S3 and stream it through our API
          const fileBuffer = await S3Service.downloadFile(s3Key);

          // Set appropriate headers
          res.setHeader('Content-Type', mimeType || 'application/octet-stream');
          res.setHeader('Content-Disposition', `inline; filename="${originalName}"`);
          res.setHeader('Cache-Control', 'private, max-age=3600'); // Cache for 1 hour
          res.setHeader('Content-Length', fileBuffer.length);

          // Ensure CORS headers are set for file serving
          const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'];
          const origin = req.headers.origin;
          if (allowedOrigins.includes(origin)) {
            res.setHeader('Access-Control-Allow-Origin', origin);
          }
          res.setHeader('Access-Control-Allow-Credentials', 'true');
          res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
          res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Session-ID, X-CSRF-Token');

          // Send file buffer
          res.send(fileBuffer);
          return;
        } catch (s3Error) {
          logger.error(`Failed to download file from S3: ${s3Key}`, s3Error);
          ResponseUtil.serverError(res, 'Failed to retrieve file from storage');
          return;
        }
      }

      // Serve local file
      if (chatMessage && chatMessage.attachmentStorageType === 'local' && chatMessage.attachmentPath) {
        const filePath = path.join(process.cwd(), chatMessage.attachmentPath);

        try {
          await fs.access(filePath);

          // Set headers for file serving
          res.setHeader('Content-Type', chatMessage.attachmentType || 'application/octet-stream');
          res.setHeader('Content-Disposition', `inline; filename="${chatMessage.attachmentName}"`);
          res.setHeader('Cache-Control', 'private, max-age=3600'); // Cache for 1 hour

          // Ensure CORS headers are set for file serving
          const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'];
          const origin = req.headers.origin;
          if (allowedOrigins.includes(origin)) {
            res.setHeader('Access-Control-Allow-Origin', origin);
          }
          res.setHeader('Access-Control-Allow-Credentials', 'true');
          res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
          res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Session-ID, X-CSRF-Token');

          // Stream the file
          const fileBuffer = await fs.readFile(filePath);
          res.setHeader('Content-Length', fileBuffer.length);
          res.send(fileBuffer);
          return;
        } catch (fileError) {
          logger.error(`Local file not found: ${filePath}`, fileError);
          ResponseUtil.notFound(res, 'File not found on storage');
          return;
        }
      }

      ResponseUtil.notFound(res, 'File not found or invalid storage configuration');
    } catch (error) {
      logger.error('Error in file access:', error);
      ResponseUtil.serverError(res, 'Failed to access file');
    }
  });


}

export { FileController };
